import axios, { AxiosResponse, AxiosError } from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://api.crefy-connect-v2.crefy.xyz/api/v1';

// Configure axios defaults
axios.defaults.timeout = 30000; // 30 second timeout
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.common['Content-Type'] = 'application/json';

export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface User {
  id: string;
  email: string;
  isVerified: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// Utility functions for validation
export const validateUserId = (userId: string): boolean => {
  // User ID should be a non-empty string with valid format
  if (!userId || typeof userId !== 'string') {
    return false;
  }
  
  // Check for common valid formats (UUID, MongoDB ObjectId, or custom format)
  const validFormats = [
    /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/, // UUID
    /^[0-9a-fA-F]{24}$/, // MongoDB ObjectId
    /^(dev_|user_)[a-zA-Z0-9_-]+$/, // Custom format with prefix
  ];
  
  return validFormats.some(format => format.test(userId));
};

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export interface AuthResponse {
  token: string;
  user: User;
}

export interface RegisterRequest {
  email: string;
  password: string;
}

export interface VerifyRequest {
  email: string;
  otp: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface Application {
  appId: string;
  name: string;
  allowedDomains: string[];
  createdAt: string;
  updatedAt?: string;
  apiKey?: string;
}

// Application with API key (for individual app responses)
export interface ApplicationWithApiKey extends Application {
  apiKey: string;
}

// Application without API key (for list responses)
export interface ApplicationListItem extends Omit<Application, 'apiKey'> {
  apiKey?: never;
}

export interface CreateApplicationRequest {
  name: string;
  allowedDomains: string[];
}

export interface UpdateApplicationRequest {
  name?: string;
  allowedDomains?: string[];
}

export interface VerifyAppRequest {
  appId: string;
  apiKey: string;
}

export interface VerifyAppResponse {
  isValid: boolean;
  appId: string;
  developerId: string;
  allowedDomains: string[];
}

// Delete application response
export interface DeleteApplicationResponse {
  success: boolean;
  message: string;
}

// Social Wallet Authentication interfaces
export interface SocialWalletEmailLoginRequest {
  email: string;
}

export interface SocialWalletEmailLoginResponse {
  success: boolean;
  message: string;
  isActive: boolean;
  walletExists: boolean;
  data?: WalletData;
  token?: string;
}

export interface WalletVerifyRequest {
  email: string;
  otp: string;
}

export interface WalletVerifyResponse {
  success: boolean;
  message: string;
  isActive: boolean;
  walletExists: boolean;
  data: WalletData;
  token: string;
}

export interface WalletData {
  walletAddress: string;
  publicKey: string;
  socialType: 'email' | 'google';
  userData: string;
}

export interface ResendOTPRequest {
  email: string;
}

export interface ResendOTPResponse {
  success: boolean;
  message: string;
}

// Developer management interfaces (updated for Swagger spec)
export interface DeveloperRegisterRequest {
  name: string;
  email: string;
  password: string;
}

export interface DeveloperRegisterResponse {
  id: string;
  name: string;
  email: string;
  isActive: boolean;
  createdAt: string;
}

export interface DeveloperLoginRequest {
  email: string;
  password: string;
}

export interface DeveloperLoginResponse {
  token: string;
  data: {
    id: string;
    name: string;
    email: string;
    isActive: boolean;
    createdAt: string;
  };
}

export interface DeveloperProfileResponse {
  id: string;
  name: string;
  email: string;
  isActive: boolean;
  createdAt: string;
}

class ApiService {
  private async makeRequest<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: unknown,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${API_BASE_URL}${endpoint}`;
      console.log(`Making API request to: ${url}`);
      
      const config = {
        method,
        url,
        data,
        headers: {
          'ngrok-skip-browser-warning': 'true', // Skip ngrok browser warning
          ...headers,
        },
      };

      const response: AxiosResponse<T> = await axios(config);
      
      console.log(`Response status: ${response.status}`);
      console.log(`Response data:`, response.data);

      // Handle successful responses
      return {
        success: true,
        data: response.data,
      };

    } catch (error) {
      console.error('API request failed:', error);
      
      if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError;
        
        // Handle HTTP error responses
        if (axiosError.response) {
          const status = axiosError.response.status;
          const responseData = axiosError.response.data as Record<string, unknown>;
          
          return {
            success: false,
            error: String(responseData?.error || responseData?.message || `HTTP ${status}: ${axiosError.message}`),
          };
        }
        
        // Handle network errors
        if (axiosError.request) {
          return {
            success: false,
            error: 'Network error. The backend service may be down or unreachable.',
          };
        }
      }
      
      // Handle other errors
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
      };
    }
  }

  // Developer Management endpoints (updated for Swagger spec)
  async register(data: DeveloperRegisterRequest): Promise<ApiResponse<DeveloperRegisterResponse>> {
    // Validate email format
    if (!validateEmail(data.email)) {
      return {
        success: false,
        error: 'Invalid email format',
      };
    }

    if (!data.password || data.password.length < 6) {
      return {
        success: false,
        error: 'Password must be at least 6 characters long',
      };
    }

    if (!data.name || data.name.trim().length === 0) {
      return {
        success: false,
        error: 'Name is required',
      };
    }

    return this.makeRequest('/developers/register', 'POST', data);
  }

  async verify(data: VerifyRequest): Promise<ApiResponse<{ success: boolean; message: string }>> {
    if (!validateEmail(data.email)) {
      return {
        success: false,
        error: 'Invalid email format',
      };
    }

    if (!data.otp || data.otp.length !== 6) {
      return {
        success: false,
        error: 'OTP must be 6 digits',
      };
    }

    return this.makeRequest('/developers/verify-otp', 'POST', data);
  }

  async login(data: DeveloperLoginRequest): Promise<ApiResponse<DeveloperLoginResponse>> {
    if (!validateEmail(data.email)) {
      return {
        success: false,
        error: 'Invalid email format',
      };
    }

    if (!data.password) {
      return {
        success: false,
        error: 'Password is required',
      };
    }

    return this.makeRequest('/developers/login', 'POST', data);
  }

  async getProfile(token: string): Promise<ApiResponse<DeveloperProfileResponse>> {
    if (!token || typeof token !== 'string') {
      return {
        success: false,
        error: 'Invalid or missing authentication token',
      };
    }

    return this.makeRequest('/developers/profile', 'GET', undefined, {
      Authorization: `Bearer ${token}`,
    });
  }

  async resendOTP(data: ResendOTPRequest): Promise<ApiResponse<ResendOTPResponse>> {
    if (!validateEmail(data.email)) {
      return {
        success: false,
        error: 'Invalid email format',
      };
    }

    return this.makeRequest('/developers/resend-otp', 'POST', data);
  }

  async deleteDeveloper(token: string): Promise<ApiResponse<{ success: boolean; message: string }>> {
    if (!token || typeof token !== 'string') {
      return {
        success: false,
        error: 'Invalid or missing authentication token',
      };
    }

    return this.makeRequest('/developers', 'DELETE', undefined, {
      Authorization: `Bearer ${token}`,
    });
  }

  // Social Wallet Authentication endpoints
  async socialWalletEmailLogin(appId: string, data: SocialWalletEmailLoginRequest, token?: string): Promise<ApiResponse<SocialWalletEmailLoginResponse>> {
    if (!appId || typeof appId !== 'string') {
      return {
        success: false,
        error: 'Invalid app ID',
      };
    }

    if (!validateEmail(data.email)) {
      return {
        success: false,
        error: 'Invalid email format',
      };
    }

    const headers: Record<string, string> = {};
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    return this.makeRequest(`/oauth/email/${appId}/email-login`, 'POST', data, headers);
  }

  async socialWalletVerify(appId: string, data: WalletVerifyRequest, token?: string): Promise<ApiResponse<WalletVerifyResponse>> {
    if (!appId || typeof appId !== 'string') {
      return {
        success: false,
        error: 'Invalid app ID',
      };
    }

    if (!validateEmail(data.email)) {
      return {
        success: false,
        error: 'Invalid email format',
      };
    }

    if (!data.otp || data.otp.length !== 6) {
      return {
        success: false,
        error: 'OTP must be 6 digits',
      };
    }

    const headers: Record<string, string> = {};
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    return this.makeRequest(`/oauth/email/${appId}/wallet-verify`, 'POST', data, headers);
  }

  async socialWalletResendOTP(appId: string, data: ResendOTPRequest): Promise<ApiResponse<ResendOTPResponse>> {
    if (!appId || typeof appId !== 'string') {
      return {
        success: false,
        error: 'Invalid app ID',
      };
    }

    if (!validateEmail(data.email)) {
      return {
        success: false,
        error: 'Invalid email format',
      };
    }

    return this.makeRequest(`/oauth/email/${appId}/resend-email-otp`, 'POST', data);
  }

  async initiateGoogleAuth(appId: string): Promise<void> {
    if (!appId || typeof appId !== 'string') {
      throw new Error('Invalid app ID');
    }

    // This will redirect to Google OAuth, so we don't return a response
    const url = `${API_BASE_URL}/oauth/google/${appId}/initiate-google-auth`;
    window.location.href = url;
  }

  // Helper method to handle Google OAuth callback result
  async getGoogleAuthResult(appId: string): Promise<ApiResponse<WalletVerifyResponse>> {
    if (!appId || typeof appId !== 'string') {
      return {
        success: false,
        error: 'Invalid app ID',
      };
    }

    // This would typically be called after Google OAuth redirect
    // The actual implementation would depend on how the callback is handled
    return this.makeRequest(`/oauth/google/${appId}/result`, 'GET');
  }

  // Application management endpoints
  async createApplication(data: CreateApplicationRequest, token: string): Promise<ApiResponse<ApplicationWithApiKey>> {
    return this.makeRequest('/apps', 'POST', data, {
      Authorization: `Bearer ${token}`,
    });
  }

  async getApplications(token: string): Promise<ApiResponse<ApplicationListItem[]>> {
    return this.makeRequest('/apps', 'GET', undefined, {
      Authorization: `Bearer ${token}`,
    });
  }

  async getApplication(appId: string, token: string): Promise<ApiResponse<ApplicationWithApiKey>> {
    if (!appId || typeof appId !== 'string') {
      return {
        success: false,
        error: 'Invalid app ID',
      };
    }

    return this.makeRequest(`/apps/${appId}`, 'GET', undefined, {
      Authorization: `Bearer ${token}`,
    });
  }

  async verifyApplication(data: VerifyAppRequest): Promise<ApiResponse<VerifyAppResponse>> {
    return this.makeRequest('/apps/verify', 'POST', data);
  }

  async deleteApplication(appId: string, token: string): Promise<ApiResponse<DeleteApplicationResponse>> {
    return this.makeRequest(`/apps/${appId}`, 'DELETE', undefined, {
      Authorization: `Bearer ${token}`,
    });
  }

  async updateApplication(appId: string, data: UpdateApplicationRequest, token: string): Promise<ApiResponse<ApplicationWithApiKey>> {
    return this.makeRequest(`/apps/${appId}`, 'PUT', data, {
      Authorization: `Bearer ${token}`,
    });
  }
}

export const apiService = new ApiService();